<!--
 * @Author: 老范
 * @Date: 2025-03-20 17:50:45
 * @LastEditors: tzs
 * @LastEditTime: 2025-08-11 13:59:35
 * @Description: 请填写简介
-->
<template>
  <div id="GEOVISContainer">
    <div class="leftCardBox">
      <ScenarioInfo
        v-show="store.state.app.ifScenarioShow"
        :scenarioInfo="scenarioParams"
      />
      <LogCard
        v-show="store.state.app.ifLogBoxShow"
        ref="logBoxRef"
        @sendMessage="setLogInfo"
      />
    </div>
    <div class="rightCardBox">
      <EntityListCard
        v-show="store.state.app.entityListShow"
        @g-db-click="focusEntity"
        :entitiesList="existEntity"
      />
      <EntityInfoCard v-show="store.state.app.EntityInfo" />
      <EntityConfig v-show="store.state.app.effectBoxShow" />
    </div>
    <CardConfig />
    <IntegratedPanelControls
      :maxDistance="maxDistance.value"
      :center="center.value"
      :existEntity="existEntity.value"
      :entitiesTypeList="entitiesTypeList.value"
    />
    <GlobalEffectConfigDialog />
    <EntityControlDialog
      :entitiesList="existEntity"
      :entitiesTypeList="entitiesTypeList"
    />
    <TaskControlDialog :entitiesList="existEntity" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, provide, computed } from 'vue'
import { useStore } from 'vuex'
import EntityControlDialog from '@/components/ControlDialog/EntityControlDialog.vue'
import TaskControlDialog from '@/components/ControlDialog/TaskControlDialog.vue'
import GlobalEffectConfigDialog from '@/components/ControlDialog/GlobalEffectConfigDialog.vue'
import ScenarioInfo from '@/components/SimCard/ScenarioInfo.vue'
import LogCard from '@/components/SimCard/LogCard.vue'
import EntityListCard from '@/components/SimCard/EntityListCard.vue'
import EntityInfoCard from '@/components/SimCard/EntityInfoCard.vue'
import EntityConfig from '@/components/SimCard/EntityConfig.vue'
import CardConfig from '@/components/SimCard/CardConfig.vue'
import IntegratedPanelControls from '@/components/SimCard/IntegratedPanelControls.vue'
import { ModelManager } from '@/utils/modelControler'
import { EffectManager } from '@/utils/effectManager'
import { CesiumManager } from '@/utils/cesiumManager'
import { WebSocketManager } from '@/utils/websocketManager'

const store = useStore()

const scenarioParams = reactive({
  scenarioDescribe: '',
  scenarioName: '',
  modeText: '',
  startTime: '',
  realTime: 0,
})

const cesiumManager = new CesiumManager();
// 将cesiumManager实例赋值给window对象，以便其他组件访问
(window as any).cesiumManager = cesiumManager;
const websocketManager = new WebSocketManager(
  (window as any).GVJ.URLS.socketServer
)

const currentEntityId = ref('')
const currentSide = ref('red')
/** 共享当前选中的实体id */
provide('currentEntityId', currentEntityId)
provide('currentSide', currentSide)
const existEntity = ref([])
const entitiesTypeList = ref([])
const logBoxRef = ref()
const center = ref([113, 40])
const maxDistance = ref(10000)

/** 跟踪实体 */
const focusEntity = (entityId: string) => cesiumManager.focusEntity(entityId)
/** 追加日志 */
const setLogInfo = (message: string) => logBoxRef.value.sendMessage(message)

// 声明WebSocket引用变量
let routeLines = new Map()
// 创建新数组确保响应式更新
const setEntityList = (list: any[]) => (existEntity.value = [...list])

// 初始化所有socket数据连接
const initWebsocket = () => {
  const effectManager = new EffectManager(
    window.viewer,
    `${(window as any).GVJ.URLS.socketServer}api/v1/data-socket/getEvents`,
    setLogInfo,
    setEntityList,
    existEntity.value
  )
  window.effectManager = effectManager
  const modelManager = new ModelManager(window.viewer, effectManager, {
    maxTrailPoints: 200,
    trailColor: Cesium.Color.RED,
  })
  window.modelManager = modelManager

  // 初始化WebSocket连接
  websocketManager.initAllWebSockets()

  // 监听自定义事件
  window.addEventListener('modelDataReceived', handleModelDataReceived)
  window.addEventListener('systemInfoReceived', handleSystemInfoReceived)
  window.addEventListener('realtimeDataReceived', handleRealtimeDataReceived)
  window.addEventListener('platformTypeReceived', handlePlatformTypeReceived)
}

// 处理模型数据接收
const handleModelDataReceived = (event: CustomEvent) => {
  const data = event.detail
  console.log(data, '初始模型和路线数据')

  scenarioParams.scenarioDescribe = data.description
  scenarioParams.scenarioName = data.name
  scenarioParams.startTime = data.startTime
  // 初始化模型
  data.init_model.forEach(item => {
    const model = existEntity.value.find(existItem => existItem.na === item.na)
    if (model) return
    existEntity.value.push(item)
    window.modelManager.createNewModel(item)
    window.modelManager.trailPrimitive.addTrail(
      item.na,
      [
        { lo: item.lo, la: item.la, al: item.al | 0 },
        { lo: item.lo, la: item.la, al: item.al | 0 },
      ],
      { si: item.si }
    )
  })

  if (data.routes?.length > 0) {
    // 清除路线
    routeLines.forEach((line, id) => {
      window.viewer.entities.remove(line)
    })
    routeLines.clear()
  }
  // 预设航路
  data.routes?.forEach(route => {
    const id = `${route.type}_initRoute`
    if (!routeLines.get(id)) {
      const routeAry = route.parameters.flat()
      const catrePosition = Cesium.Cartesian3.fromDegreesArrayHeights(routeAry)

      const spline = new Cesium.CatmullRomSpline({
        times: catrePosition.map(
          (_, index) => index / (catrePosition.length - 1)
        ),
        points: catrePosition,
      })
      const smoothPosition: Cesium.Cartesian3[] = []
      const sampleCount = 10
      for (let index = 0; index < sampleCount; index++) {
        const time = index / sampleCount
        smoothPosition.push(spline.evaluate(time))
      }
      const line = window.viewer.entities.add({
        id,
        name: '线',
        polyline: {
          width: 1.0,
          positions: catrePosition,
          material: Cesium.Color.YELLOW.withAlpha(0.7),
        },
      })
      routeLines.set(id, line)
    }
  })
  maxDistance.value = data.cameraConfig.maxDistance
  center.value = data.cameraConfig.center
  if (store.state.simulationConfig.taskStatus !== 2) {
    // 不是运行状态
    cesiumManager.viewerToAnodic(maxDistance.value, center.value)
  }
}

// 处理系统信息接收
const handleSystemInfoReceived = (event: CustomEvent) => {
  const data = event.detail
  console.log(data, '系统状态信息')
  scenarioParams.modeText = `${data.mode ? '回放' : '推演'}模式-${
    data.simulateMode ? '帧' : '事件'
  }模式`
  store.commit('SET_RUNMODE_STATUS', data.mode)
  // 更新全局taskStatus变量
  store.commit('SET_TASK_STATUS', data.taskStatus)
  if (data.taskStatus === 0) {
    // 清空
    window.modelManager.reset()
    scenarioParams.realTime = 0
    logBoxRef.value.clearList()
    existEntity.value.length = 0
    currentEntityId.value = ''
    entitiesTypeList.value = []
    routeLines.forEach((line, id) => {
      window.viewer.entities.remove(line)
    })
    routeLines.clear()
  } else if (data.taskStatus === 4) {
    // existEntity.value.length = 0
    // logBoxRef.value.clearList()
    // currentEntityId.value = ''
  } else if (data.taskStatus === 6) {
    // 全部重置
    window.modelManager.reset() // 清空
    // 清除路线
    routeLines.forEach((line, id) => {
      window.viewer.entities.remove(line)
    })
    routeLines.clear()
    existEntity.value.length = 0
    logBoxRef.value.clearList()
    currentEntityId.value = ''
  }
  let isMounted = true
  if (
    data.taskStatus === 1 ||
    ([2, 3].includes(data.taskStatus) && isMounted)
  ) {
    isMounted = false
  }
}

// 处理实时数据接收
const handleRealtimeDataReceived = (event: CustomEvent) => {
  const data = event.detail
  //console.log(data,'模型实时位置更新');

  window.modelManager.batchUpdate(data.up, data.ts)
  scenarioParams.realTime = data.ts
  // 新增：同步simTimestamp到vuex
  store.commit('SET_SIM_TIMESTAMP', data.ts)
}

// 处理平台类型数据接收
const handlePlatformTypeReceived = (event: CustomEvent) => {
  const data = event.detail
  console.log(data, '实体类型信息')
  data.forEach(i => {
    const blueType = Object.keys(i.blueSide.types)
    const redType = Object.keys(i.redSide.types)
    const typeSet = new Set([
      ...entitiesTypeList.value,
      ...blueType,
      ...redType,
    ])
    entitiesTypeList.value = Array.from(typeSet)
  })
}

// 关闭所有WebSocket连接
const closeAllWebSockets = () => {
  websocketManager.closeAllWebSockets()
  // 移除事件监听器
  window.removeEventListener('modelDataReceived', handleModelDataReceived)
  window.removeEventListener('systemInfoReceived', handleSystemInfoReceived)
  window.removeEventListener('realtimeDataReceived', handleRealtimeDataReceived)
  window.removeEventListener('platformTypeReceived', handlePlatformTypeReceived)
}

// 组件卸载时关闭所有WebSocket连接
onUnmounted(() => {
  closeAllWebSockets()
  cesiumManager.destroyEventHandler() // 组件卸载时销毁事件处理器
})

onMounted(async () => {
  window.viewer = cesiumManager.createView('GEOVISContainer')
  // 配置点击实体时显示气泡
  cesiumManager.configureBubbleDisplay(true)
  // 初始化事件处理器
  cesiumManager.initEventHandler((id: string | null) => {
    currentEntityId.value = id
  })
  initWebsocket()
})
</script>

<style lang="less" scoped>
#GEOVISContainer {
  width: 100%;
  height: 100%;
  position: absolute;
  .leftCardBox,
  .rightCardBox {
    padding-top: 5px;
    position: absolute;
    height: calc(100% - 66px);
    top: 50px;
    z-index: 1;
    font-size: 16px;
  }
  .leftCardBox {
    width: 336px;
    left: 15px;
  }
  .rightCardBox {
    right: 15px;
    width: 331px;
  }
  .cameraReset,
  .globalSimConfig {
    position: absolute;
    right: 350px;
    bottom: 74px;
    z-index: 1;
    cursor: pointer;
  }
  .globalSimConfig {
    bottom: 36px;
  }
  .entityControlBtn {
    bottom: 195px;
    right: 350px;
    background: url('/image/entity_control_icon.png');
  }
  .fullScreen {
    position: absolute;
    right: 350px;
    bottom: 112px;
    z-index: 1;
    cursor: pointer;
  }
  .taskControlBtn {
    bottom: 155px;
    background: url('/image/task_icon.png');
    right: 384px;
  }
  .entityControlBtn,
  .taskControlBtn {
    width: 34px;
    height: 34px;
    border: 0;
    position: absolute;
    z-index: 1;
    img {
      width: 36px;
      height: 34px;
    }
  }
  .panelVisibleIcon {
    bottom: 235px;
    position: absolute;
    z-index: 1;
  }
}
</style>
