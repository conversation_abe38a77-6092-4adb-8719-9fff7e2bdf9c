<template>
  <div class="entityCardBox">
    <el-row class="title">
      <span>实体列表</span>
    </el-row>
    <div class="contentBox">
      <el-collapse v-model="activeNames" class="entity-collapse">
        <el-collapse-item
          v-for="item in sideList"
          :key="item.value"
          :title="item.label"
          :name="item.value"
          :disabled="!sideEntitiesBySide[item.value]?.length"
        >
          <template v-if="sideEntitiesBySide[item.value]?.length">
            <div class="entityList">
              <div
                v-for="entity in sideEntitiesBySide[item.value]"
                :key="entity"
                class="entity"
                :title="entity"
                :style="{
                  color: '#fff',
                }"
                @click="handleNodeClick(entity)"
                @dblclick="handleDbNodeClick(entity)"
              >
                {{ entity }}
              </div>
            </div>
          </template>
          <template v-else>
            <div class="emptyBox">
              <Empty />
            </div>
          </template>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, inject } from 'vue'
import { ElCollapse, ElCollapseItem } from 'element-plus'

// 控制折叠面板的展开状态
const activeNames = ref<string[]>(['aircraft'])
import Empty from '@/components/Empty.vue'
const emit = defineEmits(['gClick', 'gDbClick'])
import { useStore } from 'vuex'

const store = useStore()
const props = defineProps({
  entitiesList: {
    type: Object,
    default: '',
  },
})

const sideList = [
  {
    label: '飞行器',
    value: 'aircraft',
    color: '#fe7f7f',
  },
  {
    label: '通导监设备',
    value: 'navigation',
    color: '#00a4c5',
  },
  {
    label: '地面设施',
    value: 'ground',
    color: '#4caf50',
  },
]
// 按阵营分组的实体列表
const sideEntitiesBySide = computed(() => {
  const result: Record<string, string[]> = {}
  sideList.forEach(item => {
    result[item.value] = []
  })

  if (Array.isArray(props.entitiesList)) {
    props.entitiesList.forEach(entity => {
      let sideValue = 'aircraft' // 默认飞行器
      if (entity.si === 'blue') {
        sideValue = 'navigation'
      } else if (entity.si === 'ground') {
        sideValue = 'ground'
      }
      if (result[sideValue]) {
        result[sideValue].push(entity.na)
      }
    })
  }
  return result
})

// 为了兼容现有代码保留的计算属性
const sideEntities = computed(() => {
  return sideEntitiesBySide.value[currentSide.value] || []
})
const sideColor = computed(() => {
  return sideList.find(i => i.value === currentSide.value)?.color || '#fff'
})
const currentEntityId = inject('currentEntityId', ref(''))
// 使用新的默认值初始化当前阵营
const currentSide = inject('currentSide', ref('aircraft'))
/** 单击实体 */
const handleNodeClick = (na: string) => {
  currentEntityId.value = na
}
/** 双击实体 */
const handleDbNodeClick = (na: string) => {
  emit('gDbClick', na)
}

watch(
  () => currentEntityId.value,
  val => {
    if (!val) return
    const entity = window.viewer.entities.getById(val)
    const properties = entity && entity.properties
    if (!properties) return
    const siValue = properties.si?.getValue()
    // 映射旧的si值到新的阵营值
    if (siValue === 'red') {
      currentSide.value = 'aircraft'
    } else if (siValue === 'blue') {
      currentSide.value = 'navigation'
    } else if (siValue === 'ground') {
      currentSide.value = 'ground'
    }
  }
)

watch(
  () => store.state.simulationConfig.taskStatus,
  val => {
    if (val === 4) {
      // currentEntityId.value = ''
      currentSide.value = 'aircraft'
    }
  }
)
watch(
  () => props.entitiesList,
  val => {
    if (Array.isArray(val) && val.length && !currentEntityId.value) {
      // 优先选当前阵营下的第一个
      let first = val.find(i => {
        if (currentSide.value === 'aircraft') {
          return i.si === 'red' // 飞行器对应原来的红方
        } else if (currentSide.value === 'navigation') {
          return i.si === 'blue' // 通导监设备对应原来的蓝方
        } else if (currentSide.value === 'ground') {
          return i.si === 'ground' // 地面设施是新增的
        }
        return false
      })
      if (!first) {
        // 没有当前阵营的，选其他阵营第一个
        first = val[0]
      }
      if (first) {
        currentEntityId.value = first.na
      }
    }
  },
  { immediate: true, deep: true }
)
</script>

<style lang="less" scoped>
.entityCardBox {
  height: calc(100% - 303px - 200px);
  background: url('/image/box_bg.png') no-repeat 0px 37px;
  background-size: 100% 100%;
  .title {
    height: 37px;
    font-size: 16px;
    font-family: Bold;
    font-style: oblique;
    align-items: center;
    padding-left: 35px;
    padding-bottom: 4px;
    background: url('/image/title_bg.png') no-repeat;
    span {
      background: url('/image/title_font_bg.png') no-repeat 15px 5px;
    }
  }
  .contentBox {
    height: calc(100% - 32px);
    overflow: auto;
    padding: 5px 4px;
    .entity-collapse {
      border: none;
      background: transparent;
      :deep(.el-collapse-item) {
        margin-bottom: 5px;
        border: 0;
        overflow: hidden;
      }
      :deep(.el-collapse-item__wrap) {
        background-color: transparent !important;
        border: 0;
      }
      :deep(.el-collapse-item__header) {
        background: linear-gradient(to right, #1d3b6ce3, #1d3b6c84, #1d3b6c40);
        color: #fff;
        height: 30px;
        line-height: 30px;
        padding-left: 15px;
        position: relative;
        border: 0 !important;
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          height: 100%;
          width: 3px;
        }
        &.is-active {
          background: url('/image/selected_bg.png') repeat-x;
          box-shadow: none;
        }
      }
      :deep(.el-collapse-item__content) {
        padding: 0 !important;
        background: transparent !important;
      }
      // 飞行器分类样式
      .el-collapse-item:nth-child(1) .el-collapse-item__header {
        &::before {
          background-color: #fe7f7f;
        }
      }
      // 通导监设备分类样式
      .el-collapse-item:nth-child(2) .el-collapse-item__header {
        &::before {
          background-color: #00a4c5;
        }
      }
      // 地面设施分类样式
      .el-collapse-item:nth-child(3) .el-collapse-item__header {
        &::before {
          background-color: #4caf50;
        }
      }
    }
    .entityList {
      height: calc(100% - 30px - 5px);
      border: 0;
      overflow-y: auto;
      margin: 5px 0px;
      .entity {
        padding: 0px 10px;
        cursor: pointer;
        line-height: 28px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .entity:hover {
        background: url(/image/entitiy_bg.png);
      }
    }
    .emptyBox {
      padding-top: 50px;
      height: calc(100% - 30px - 5px);
      border: 1px solid var(--app-border-color);
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }
}
</style>
